/**
  * This file is used to fix the duplicate symbol errors in system_stm32f1xx.c
  * It uses weak attribute to allow the linker to choose only one implementation
  */

#include "stm32f1xx.h"

// Use weak attribute to allow the linker to choose only one implementation
__attribute__((weak)) uint32_t SystemCoreClock = 8000000;
__attribute__((weak)) const uint8_t AHBPrescTable[16] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9};
__attribute__((weak)) const uint8_t APBPrescTable[8] = {0, 0, 0, 0, 1, 2, 3, 4};

__attribute__((weak)) void SystemInit(void)
{
  // Empty implementation
}

__attribute__((weak)) void SystemCoreClockUpdate(void)
{
  // Empty implementation
}