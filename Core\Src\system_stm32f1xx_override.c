// This file overrides the duplicate symbols from system_stm32f1xx.c
// It will be compiled but won't provide any actual implementation

#include "stm32f1xx.h"

// Define these symbols with 'weak' attribute so they can be overridden by the real implementation
__attribute__((weak)) uint32_t SystemCoreClock = 8000000;
__attribute__((weak)) const uint8_t AHBPrescTable[16] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9};
__attribute__((weak)) const uint8_t APBPrescTable[8] = {0, 0, 0, 0, 1, 2, 3, 4};

__attribute__((weak)) void SystemInit(void)
{
  // Empty implementation
}

__attribute__((weak)) void SystemCoreClockUpdate(void)
{
  // Empty implementation
}