/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

#include "bno080_hal.h"
#include "bno080_test.h"
#include "bno080_integration_test.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

extern UART_HandleTypeDef huart2;  // 调试输出UART

// BNO080数据结构
BNO080_Data_t bno080_data;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

void BNO080_GPIO_Config(void);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_USART2_UART_Init();
  MX_I2C1_Init();
  MX_TIM2_Init();
  /* USER CODE BEGIN 2 */

    // 配置BNO080的GPIO引脚，设置为I2C模式
    BNO080_GPIO_Config();
    
    // 使用串口2进行调试输出
    my_printf(&huart2, "BNO080 I2C Example Started\r\n");
    
    // 检测BNO080 I2C设备是否就绪
    if (BNO080_I2C_IsReady() == HAL_OK)
    {
        my_printf(&huart2, "BNO080 I2C设备检测成功\r\n");
        
        // 发送复位命令
        if (BNO080_I2C_Reset() == HAL_OK)
        {
            my_printf(&huart2, "BNO080复位成功\r\n");
        }
        
        // 检查设备状态
        if (BNO080_I2C_CheckStatus() == HAL_OK)
        {
            my_printf(&huart2, "BNO080状态检查成功\r\n");
            
            // 读取产品ID
            uint8_t product_id[20] = {0};
            uint16_t id_length = 0;
            
            if (BNO080_I2C_ReadProductID(product_id, &id_length) == HAL_OK)
            {
                my_printf(&huart2, "BNO080产品ID读取成功，长度: %d\r\n", id_length);
                
                // 打印产品ID (十六进制)
                my_printf(&huart2, "产品ID: ");
                for (uint16_t i = 0; i < id_length; i++)
                {
                    my_printf(&huart2, "%02X ", product_id[i]);
                }
                my_printf(&huart2, "\r\n");
                
                // 配置旋转向量报告 (100Hz)
                if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
                {
                    my_printf(&huart2, "旋转向量报告配置成功 (100Hz)\r\n");
                    
                    // 启动定时器，定期读取传感器数据
                    HAL_TIM_Base_Start_IT(&htim2);
                    my_printf(&huart2, "定时器已启动，频率: 100Hz\r\n");
                }
                else
                {
                    my_printf(&huart2, "旋转向量报告配置失败\r\n");
                    
                    // 尝试配置游戏旋转向量报告 (备选方案)
                    if (BNO080_I2C_ConfigureGameRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
                    {
                        my_printf(&huart2, "游戏旋转向量报告配置成功 (100Hz)\r\n");
                        
                        // 启动定时器，定期读取传感器数据
                        HAL_TIM_Base_Start_IT(&htim2);
                        my_printf(&huart2, "定时器已启动，频率: 100Hz\r\n");
                    }
                }
            }
        }
    }
    else
    {
        my_printf(&huart2, "BNO080 I2C设备检测失败\r\n");
    }
    
    // 运行单元测试 (可选)
    // 取消注释下面的行来运行测试
    // BNO080_RunAllTests();
    
    // 运行集成测试 (可选)
    // 取消注释下面的行来运行集成测试
    // BNO080_RunIntegrationTests();
    
    my_printf(&huart2, "等待BNO080数据...\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

        // 获取BNO080数据
        BNO080_GetData(&bno080_data);
        
        // 检查是否有新数据
        if(bno080_data.new_data_flag)
        {
            bno080_data.new_data_flag = 0;
            
            // 输出欧拉角数据
            my_printf(&huart2,"%.2f,%.2f,%.2f\n",bno080_data.yaw, bno080_data.pitch, bno080_data.roll);
        }
        
        HAL_Delay(100);  // 100ms延时	  
	  
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
