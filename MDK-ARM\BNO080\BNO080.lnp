--cpu Cortex-M3
"bno080\startup_stm32f103xb.o"
"bno080\system_stm32f1xx.o"
"bno080\main.o"
"bno080\gpio.o"
"bno080\i2c.o"
"bno080\tim.o"
"bno080\usart.o"
"bno080\stm32f1xx_hal_gpio_ex.o"
"bno080\stm32f1xx_hal_i2c.o"
"bno080\stm32f1xx_hal.o"
"bno080\stm32f1xx_hal_rcc.o"
"bno080\stm32f1xx_hal_rcc_ex.o"
"bno080\stm32f1xx_hal_gpio.o"
"bno080\stm32f1xx_hal_dma.o"
"bno080\stm32f1xx_hal_cortex.o"
"bno080\stm32f1xx_hal_pwr.o"
"bno080\stm32f1xx_hal_flash.o"
"bno080\stm32f1xx_hal_flash_ex.o"
"bno080\stm32f1xx_hal_exti.o"
"bno080\stm32f1xx_hal_tim.o"
"bno080\stm32f1xx_hal_tim_ex.o"
"bno080\stm32f1xx_hal_uart.o"
"bno080\bno080_hal.o"
"bno080\bno080_integration_test.o"
"bno080\bno080_test.o"
--strict --scatter "BNO080\BNO080.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "BNO080.map" -o BNO080\BNO080.axf