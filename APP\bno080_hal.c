#include "bno080_hal.h"
#include <string.h>
#include <math.h>
#include "usart.h"
#include "i2c.h"
#include "tim.h"

// 全局变量
static BNO080_Data_t g_bno080_data = {0};
static uint8_t g_bno080_i2c_address = BNO080_I2C_ADDR_ADR_LOW; // 默认使用低地址
static BNO080_I2C_State_t g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
static uint8_t g_i2c_rx_buffer[32]; // I2C接收缓冲区
static BNO080_ErrorStats_t g_error_stats = {0}; // 错误统计
static uint8_t g_device_online = 0; // 设备在线状态
static BNO080_PerformanceStats_t g_performance_stats = {0}; // 性能统计
static BNO080_DebugLevel_t g_debug_level = BNO080_DEBUG_OFF; // 调试级别

//bno080:
//协议选择PS0,PS1配置:
//PS0=0,PS1=0: I2C模式
//PS0=1,PS1=0: UART-RVC模式
//PS0=0,PS1=1: UART-HID模式
//PS0=1,PS1=1: SPI模式
void BNO080_GPIO_Config(void)
{
    // 配置为I2C模式: PS0=0, PS1=0
    
    // PB0 连接PS0 设为低电平
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);
    
    // PB1 连接PS1 设为低电平
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);
    
    // 等待一段时间让配置生效
    HAL_Delay(10);
    
    my_printf(&huart2, "BNO080 GPIO配置为I2C模式\r\n");
}

/**
 * @brief  初始化BNO080
 * @note   需要在main.c中先初始化I2C和GPIO
 */
void BNO080_Init(void)
{
    // 初始化全局数据结构
    memset(&g_bno080_data, 0, sizeof(BNO080_Data_t));
    g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
    
    // 配置BNO080的GPIO引脚为I2C模式
    BNO080_GPIO_Config();
    
    // 检测BNO080 I2C设备是否就绪
    if (BNO080_I2C_IsReady() != HAL_OK)
    {
        my_printf(&huart2, "BNO080 I2C初始化失败：设备未找到\r\n");
        return;
    }
    
    // 发送复位命令
    if (BNO080_I2C_Reset() != HAL_OK)
    {
        my_printf(&huart2, "BNO080 I2C初始化失败：复位失败\r\n");
        return;
    }
    
    // 等待复位完成
    HAL_Delay(100);
    
    // 检查设备状态
    if (BNO080_I2C_CheckStatus() != HAL_OK)
    {
        my_printf(&huart2, "BNO080 I2C初始化失败：状态检查失败\r\n");
        return;
    }
    
    // 配置旋转向量报告 (100Hz)
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
    {
        my_printf(&huart2, "BNO080 I2C初始化成功：旋转向量报告已配置\r\n");
    }
    else
    {
        // 尝试配置游戏旋转向量报告 (备选方案)
        if (BNO080_I2C_ConfigureGameRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
        {
            my_printf(&huart2, "BNO080 I2C初始化成功：游戏旋转向量报告已配置\r\n");
        }
        else
        {
            my_printf(&huart2, "BNO080 I2C初始化失败：传感器配置失败\r\n");
            return;
        }
    }
    
    my_printf(&huart2, "BNO080 I2C初始化完成\r\n");
}

/**
 * @brief  获取BNO080数据
 * @param  data_ptr: 指向数据结构的指针
 */
void BNO080_GetData(BNO080_Data_t* data_ptr)
{
    // 临界区保护
    __disable_irq();
    memcpy(data_ptr, &g_bno080_data, sizeof(BNO080_Data_t));
    __enable_irq();
}

/**
 * @brief  处理接收到的数据
 * @param  data_length: 接收到的数据长度
 */
void BNO080_ProcessReceivedData(uint16_t data_length)
{
    // I2C模式下，直接启动非阻塞读取
    BNO080_I2C_StartNonBlockingRead();
}



/**
 * @brief  检查BNO080 I2C设备是否就绪
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_IsReady(void)
{
    // 首先尝试低地址 (ADR引脚接GND)
    HAL_StatusTypeDef status = I2C_IsDeviceReady(BNO080_I2C_ADDR_ADR_LOW << 1, 3);
    
    if (status == HAL_OK)
    {
        g_bno080_i2c_address = BNO080_I2C_ADDR_ADR_LOW;
        g_device_online = 1;
        my_printf(&huart2, "BNO080 I2C设备在地址0x%02X就绪\r\n", BNO080_I2C_ADDR_ADR_LOW);
        return HAL_OK;
    }
    
    // 如果低地址失败，尝试高地址 (ADR引脚接VCC)
    status = I2C_IsDeviceReady(BNO080_I2C_ADDR_ADR_HIGH << 1, 3);
    
    if (status == HAL_OK)
    {
        g_bno080_i2c_address = BNO080_I2C_ADDR_ADR_HIGH;
        g_device_online = 1;
        my_printf(&huart2, "BNO080 I2C设备在地址0x%02X就绪\r\n", BNO080_I2C_ADDR_ADR_HIGH);
        return HAL_OK;
    }
    
    // 设备未找到，触发错误处理
    BNO080_ErrorHandler(BNO080_ERROR_DEVICE_NOT_FOUND);
    my_printf(&huart2, "BNO080 I2C设备未找到\r\n");
    return HAL_ERROR;
}

/**
 * @brief  发送复位命令到BNO080
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_Reset(void)
{
    uint8_t reset_cmd[4] = {0};
    
    // 构建复位命令
    reset_cmd[0] = 1;  // 报告ID为1表示复位命令
    reset_cmd[1] = 0;  // 保留字节
    reset_cmd[2] = 1;  // 复位类型: 1=软复位
    reset_cmd[3] = 0;  // 保留字节
    
    // 发送复位命令到命令寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            reset_cmd, 
                                            sizeof(reset_cmd), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "BNO080复位命令发送失败\r\n");
        return status;
    }
    
    // 等待设备复位完成
    HAL_Delay(200);
    my_printf(&huart2, "BNO080复位命令已发送\r\n");
    
    return HAL_OK;
}

/**
 * @brief  读取BNO080产品ID
 * @param  product_id: 产品ID缓冲区
 * @param  length: 读取的数据长度
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ReadProductID(uint8_t* product_id, uint16_t* length)
{
    uint8_t cmd[2] = {0};
    uint8_t response[20] = {0};
    uint16_t resp_len = 0;
    
    // 构建获取产品ID命令
    cmd[0] = BNO080_REPORT_PRODUCT_ID;  // 产品ID报告
    cmd[1] = 0;  // 序列号
    
    // 发送命令到输出报告寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_OUTPUT_REPORT, 
                                            1, 
                                            cmd, 
                                            sizeof(cmd), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "发送获取产品ID命令失败\r\n");
        return status;
    }
    
    // 等待设备处理命令
    HAL_Delay(50);
    
    // 从输入报告寄存器读取响应
    status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                         BNO080_REG_INPUT_REPORT, 
                         1, 
                         response, 
                         sizeof(response), 
                         100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "读取产品ID响应失败\r\n");
        return status;
    }
    
    // 检查响应是否为产品ID报告
    if (response[0] != BNO080_REPORT_PRODUCT_ID)
    {
        my_printf(&huart2, "收到的不是产品ID报告\r\n");
        return HAL_ERROR;
    }
    
    // 复制产品ID数据
    resp_len = response[1];  // 第二个字节包含数据长度
    if (resp_len > 0 && resp_len <= 18)  // 确保长度合理
    {
        memcpy(product_id, &response[2], resp_len);
        *length = resp_len;
        
        my_printf(&huart2, "成功读取产品ID\r\n");
        return HAL_OK;
    }
    
    my_printf(&huart2, "产品ID数据长度无效\r\n");
    return HAL_ERROR;
}

/**
 * @brief  检查BNO080状态
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_CheckStatus(void)
{
    uint8_t status_reg = 0;
    
    // 读取状态寄存器
    HAL_StatusTypeDef status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                                           0x00,  // 状态寄存器地址
                                           1, 
                                           &status_reg, 
                                           1, 
                                           100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "读取BNO080状态失败\r\n");
        return status;
    }
    
    // 检查状态寄存器的值
    if ((status_reg & 0x01) == 0)  // 检查就绪位
    {
        my_printf(&huart2, "BNO080未就绪\r\n");
        return HAL_ERROR;
    }
    
    my_printf(&huart2, "BNO080状态正常\r\n");
    return HAL_OK;
}
/**
 * @brief  配置BNO080旋转向量报告
 * @param  report_interval: 报告间隔(微秒)
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ConfigureRotationVector(uint32_t report_interval)
{
    uint8_t command[17] = {0};
    
    // 构建设置特征命令
    command[0] = BNO080_CMD_SET_FEATURE;  // 设置特征命令
    command[1] = BNO080_SENSOR_ROTATION_VECTOR;  // 旋转向量传感器
    
    // 设置报告间隔 (微秒，小端格式)
    command[2] = (uint8_t)(report_interval & 0xFF);
    command[3] = (uint8_t)((report_interval >> 8) & 0xFF);
    command[4] = (uint8_t)((report_interval >> 16) & 0xFF);
    command[5] = (uint8_t)((report_interval >> 24) & 0xFF);
    
    // 发送命令到命令寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            command, 
                                            sizeof(command), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "配置旋转向量报告失败\r\n");
        return status;
    }
    
    my_printf(&huart2, "成功配置旋转向量报告，间隔: %lu微秒\r\n", report_interval);
    return HAL_OK;
}

/**
 * @brief  配置BNO080游戏旋转向量报告
 * @param  report_interval: 报告间隔(微秒)
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ConfigureGameRotationVector(uint32_t report_interval)
{
    uint8_t command[17] = {0};
    
    // 构建设置特征命令
    command[0] = BNO080_CMD_SET_FEATURE;  // 设置特征命令
    command[1] = BNO080_SENSOR_GAME_ROTATION_VECTOR;  // 游戏旋转向量传感器
    
    // 设置报告间隔 (微秒，小端格式)
    command[2] = (uint8_t)(report_interval & 0xFF);
    command[3] = (uint8_t)((report_interval >> 8) & 0xFF);
    command[4] = (uint8_t)((report_interval >> 16) & 0xFF);
    command[5] = (uint8_t)((report_interval >> 24) & 0xFF);
    
    // 发送命令到命令寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            command, 
                                            sizeof(command), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "配置游戏旋转向量报告失败\r\n");
        return status;
    }
    
    my_printf(&huart2, "成功配置游戏旋转向量报告，间隔: %lu微秒\r\n", report_interval);
    return HAL_OK;
}/**

 * @brief  读取BNO080传感器数据
 * @param  data_buffer: 数据缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ReadSensorData(uint8_t* data_buffer, uint16_t buffer_size)
{
    // 检查参数
    if (data_buffer == NULL || buffer_size < sizeof(BNO080_RotationVector_t))
    {
        return HAL_ERROR;
    }
    
    // 从输入报告寄存器读取数据
    HAL_StatusTypeDef status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                                           BNO080_REG_INPUT_REPORT, 
                                           1, 
                                           data_buffer, 
                                           buffer_size, 
                                           100);
    
    if (status != HAL_OK)
    {
        g_bno080_data.error_count++;
        my_printf(&huart2, "读取传感器数据失败, 错误码: %d\r\n", status);
        return status;
    }
    
    // 检查报告ID是否为旋转向量或游戏旋转向量
    if (data_buffer[0] != BNO080_SENSOR_ROTATION_VECTOR && 
        data_buffer[0] != BNO080_SENSOR_GAME_ROTATION_VECTOR)
    {
        // 不是我们期望的报告类型
        return HAL_ERROR;
    }
    
    return HAL_OK;
}

/**
 * @brief  启动非阻塞I2C数据读取
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_StartNonBlockingRead(void)
{
    // 检查当前状态
    if (g_bno080_i2c_state != BNO080_I2C_STATE_IDLE)
    {
        return HAL_BUSY;
    }
    
    // 更新状态
    g_bno080_i2c_state = BNO080_I2C_STATE_READING;
    
    // 启动非阻塞读取
    HAL_StatusTypeDef status = HAL_I2C_Mem_Read_IT(&hi2c1, 
                                                  g_bno080_i2c_address << 1, 
                                                  BNO080_REG_INPUT_REPORT, 
                                                  I2C_MEMADD_SIZE_8BIT, 
                                                  g_i2c_rx_buffer, 
                                                  sizeof(BNO080_RotationVector_t));
    
    if (status != HAL_OK)
    {
        g_bno080_i2c_state = BNO080_I2C_STATE_ERROR;
        g_bno080_data.error_count++;
        my_printf(&huart2, "启动非阻塞读取失败, 错误码: %d\r\n", status);
    }
    
    return status;
}

/**
 * @brief  处理I2C接收到的数据
 * @param  data: 数据指针
 * @param  length: 数据长度
 */
void BNO080_I2C_ProcessData(uint8_t* data, uint16_t length)
{
    // 检查参数
    if (data == NULL || length < sizeof(BNO080_RotationVector_t))
    {
        return;
    }
    
    // 检查报告ID
    if (data[0] != BNO080_SENSOR_ROTATION_VECTOR && 
        data[0] != BNO080_SENSOR_GAME_ROTATION_VECTOR)
    {
        // 不是我们期望的报告类型
        return;
    }
    
    // 将数据转换为旋转向量结构体
    BNO080_RotationVector_t* rv = (BNO080_RotationVector_t*)data;
    
    // 从Q14格式转换为浮点数 (优化：使用位移代替除法)
    const float q14_scale = 1.0f / 16384.0f;
    float qx = (float)rv->i_component * q14_scale;
    float qy = (float)rv->j_component * q14_scale;
    float qz = (float)rv->k_component * q14_scale;
    float qw = (float)rv->real_component * q14_scale;
    
    // 四元数归一化 (优化：使用快速平方根倒数)
    float norm_sq = qw*qw + qx*qx + qy*qy + qz*qz;
    if (norm_sq > 1e-6f)  // 避免除零
    {
        float inv_norm = 1.0f / sqrtf(norm_sq);
        qw *= inv_norm;
        qx *= inv_norm;
        qy *= inv_norm;
        qz *= inv_norm;
    }
    
    // 四元数转欧拉角 (优化：预计算常用项)
    const float rad_to_deg = 180.0f / 3.14159265358979323846f;
    
    // 预计算常用项
    float qx_qy = qx * qy;
    float qw_qz = qw * qz;
    float qx_qx = qx * qx;
    float qy_qy = qy * qy;
    float qz_qz = qz * qz;
    float qw_qx = qw * qx;
    float qy_qz = qy * qz;
    float qw_qy = qw * qy;
    float qz_qx = qz * qx;
    
    // 计算欧拉角
    float yaw = atan2f(2.0f * (qw_qz + qx_qy), 1.0f - 2.0f * (qy_qy + qz_qz)) * rad_to_deg;
    float pitch = asinf(2.0f * (qw_qy - qz_qx)) * rad_to_deg;
    float roll = atan2f(2.0f * (qw_qx + qy_qz), 1.0f - 2.0f * (qx_qx + qy_qy)) * rad_to_deg;
    
    // 更新全局数据结构
    __disable_irq();
    g_bno080_data.yaw = yaw;
    g_bno080_data.pitch = pitch;
    g_bno080_data.roll = roll;
    g_bno080_data.new_data_flag = 1;
    g_bno080_data.last_update_time = HAL_GetTick();
    __enable_irq();
}

/**
 * @brief  I2C主机接收完成回调函数
 * @param  hi2c: I2C句柄
 */
void HAL_I2C_MasterRxCpltCallback(I2C_HandleTypeDef *hi2c)
{
    if (hi2c->Instance == hi2c1.Instance)
    {
        if (g_bno080_i2c_state == BNO080_I2C_STATE_READING)
        {
            // 更新状态
            g_bno080_i2c_state = BNO080_I2C_STATE_PROCESSING;
            
            // 处理接收到的数据
            BNO080_I2C_ProcessData(g_i2c_rx_buffer, sizeof(BNO080_RotationVector_t));
            
            // 恢复空闲状态
            g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
        }
    }
}

/**
 * @brief  I2C错误回调函数
 * @param  hi2c: I2C句柄
 */
void HAL_I2C_ErrorCallback(I2C_HandleTypeDef *hi2c)
{
    if (hi2c->Instance == hi2c1.Instance)
    {
        // 记录错误
        g_bno080_data.error_count++;
        
        // 恢复空闲状态
        g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
        
        my_printf(&huart2, "I2C通信错误, 错误码: %d\r\n", hi2c->ErrorCode);
    }
}/**

 * @brief  错误处理函数
 * @param  error: 错误类型
 */
void BNO080_ErrorHandler(BNO080_Error_t error)
{
    // 更新错误统计
    g_error_stats.total_error_count++;
    g_error_stats.last_error = error;
    g_error_stats.last_error_time = HAL_GetTick();
    
    // 根据错误类型进行分类统计
    switch (error)
    {
        case BNO080_ERROR_I2C_TIMEOUT:
            g_error_stats.i2c_timeout_count++;
            my_printf(&huart2, "BNO080错误: I2C超时\r\n");
            break;
            
        case BNO080_ERROR_I2C_NACK:
            g_error_stats.i2c_nack_count++;
            my_printf(&huart2, "BNO080错误: I2C NACK\r\n");
            break;
            
        case BNO080_ERROR_DEVICE_NOT_FOUND:
            g_error_stats.device_offline_count++;
            g_device_online = 0;
            my_printf(&huart2, "BNO080错误: 设备未找到\r\n");
            break;
            
        case BNO080_ERROR_INVALID_DATA:
            g_error_stats.invalid_data_count++;
            my_printf(&huart2, "BNO080错误: 无效数据\r\n");
            break;
            
        case BNO080_ERROR_CHECKSUM_FAIL:
            g_error_stats.invalid_data_count++;
            my_printf(&huart2, "BNO080错误: 校验和失败\r\n");
            break;
            
        case BNO080_ERROR_CONFIG_FAIL:
            my_printf(&huart2, "BNO080错误: 配置失败\r\n");
            break;
            
        default:
            my_printf(&huart2, "BNO080错误: 未知错误\r\n");
            break;
    }
    
    // 尝试恢复
    if (g_error_stats.total_error_count % 5 == 0)  // 每5次错误尝试一次恢复
    {
        BNO080_RecoveryAttempt();
    }
}

/**
 * @brief  恢复尝试函数
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_RecoveryAttempt(void)
{
    g_error_stats.recovery_count++;
    my_printf(&huart2, "BNO080开始恢复尝试 #%lu\r\n", g_error_stats.recovery_count);
    
    // 重置I2C状态机
    g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
    
    // 检查设备是否在线
    if (BNO080_I2C_IsReady() != HAL_OK)
    {
        BNO080_ErrorHandler(BNO080_ERROR_DEVICE_NOT_FOUND);
        return HAL_ERROR;
    }
    
    // 设备重新上线
    g_device_online = 1;
    
    // 发送复位命令
    if (BNO080_I2C_Reset() != HAL_OK)
    {
        BNO080_ErrorHandler(BNO080_ERROR_CONFIG_FAIL);
        return HAL_ERROR;
    }
    
    // 等待复位完成
    HAL_Delay(100);
    
    // 重新配置传感器
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) != HAL_OK)
    {
        // 尝试配置游戏旋转向量报告 (备选方案)
        if (BNO080_I2C_ConfigureGameRotationVector(BNO080_REPORT_INTERVAL_10MS) != HAL_OK)
        {
            BNO080_ErrorHandler(BNO080_ERROR_CONFIG_FAIL);
            return HAL_ERROR;
        }
    }
    
    my_printf(&huart2, "BNO080恢复成功\r\n");
    return HAL_OK;
}

/**
 * @brief  获取错误统计信息
 * @param  stats: 错误统计结构体指针
 */
void BNO080_GetErrorStats(BNO080_ErrorStats_t* stats)
{
    if (stats != NULL)
    {
        __disable_irq();
        memcpy(stats, &g_error_stats, sizeof(BNO080_ErrorStats_t));
        __enable_irq();
    }
}

/**
 * @brief  清除错误统计信息
 */
void BNO080_ClearErrorStats(void)
{
    __disable_irq();
    memset(&g_error_stats, 0, sizeof(BNO080_ErrorStats_t));
    __enable_irq();
    
    my_printf(&huart2, "BNO080错误统计已清除\r\n");
}

/**
 * @brief  检查设备是否在线
 * @retval 1: 在线, 0: 离线
 */
uint8_t BNO080_IsDeviceOnline(void)
{
    return g_device_online;
}

/**
**
 * @brief  进入低功耗模式
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_EnterLowPowerMode(void)
{
    // 停止定时器以节省功耗
    HAL_TIM_Base_Stop_IT(&htim2);
    
    // 发送低功耗命令到BNO080
    uint8_t low_power_cmd[4] = {0x02, 0x00, 0x01, 0x00}; // 进入睡眠模式
    
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            low_power_cmd, 
                                            sizeof(low_power_cmd), 
                                            100);
    
    if (status == HAL_OK)
    {
        my_printf(&huart2, "BNO080已进入低功耗模式\r\n");
    }
    else
    {
        my_printf(&huart2, "BNO080进入低功耗模式失败\r\n");
    }
    
    return status;
}

/**
 * @brief  退出低功耗模式
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_ExitLowPowerMode(void)
{
    // 发送唤醒命令到BNO080
    uint8_t wake_cmd[4] = {0x02, 0x00, 0x00, 0x00}; // 退出睡眠模式
    
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            wake_cmd, 
                                            sizeof(wake_cmd), 
                                            100);
    
    if (status == HAL_OK)
    {
        // 等待设备唤醒
        HAL_Delay(50);
        
        // 重新启动定时器
        HAL_TIM_Base_Start_IT(&htim2);
        
        my_printf(&huart2, "BNO080已退出低功耗模式\r\n");
    }
    else
    {
        my_printf(&huart2, "BNO080退出低功耗模式失败\r\n");
    }
    
    return status;
}

/**
 * @brief  优化性能设置
 */
void BNO080_OptimizePerformance(void)
{
    // 优化I2C时钟频率 (已在初始化时设置为400kHz)
    
    // 优化中断优先级
    HAL_NVIC_SetPriority(TIM2_IRQn, 1, 0);  // 设置较高优先级
    HAL_NVIC_SetPriority(I2C1_EV_IRQn, 2, 0);  // I2C事件中断
    HAL_NVIC_SetPriority(I2C1_ER_IRQn, 2, 0);  // I2C错误中断
    
    // 启用I2C快速模式
    hi2c1.Init.ClockSpeed = 400000;  // 400kHz快速模式
    
    my_printf(&huart2, "BNO080性能优化已应用\r\n");
}

/**
**
 * @brief  设置调试级别
 * @param  level: 调试级别
 */
void BNO080_SetDebugLevel(BNO080_DebugLevel_t level)
{
    g_debug_level = level;
    
    switch (level)
    {
        case BNO080_DEBUG_OFF:
            my_printf(&huart2, "BNO080调试已关闭\r\n");
            break;
        case BNO080_DEBUG_BASIC:
            my_printf(&huart2, "BNO080调试级别: 基础\r\n");
            break;
        case BNO080_DEBUG_DETAILED:
            my_printf(&huart2, "BNO080调试级别: 详细\r\n");
            break;
        case BNO080_DEBUG_VERBOSE:
            my_printf(&huart2, "BNO080调试级别: 详尽\r\n");
            break;
    }
}

/**
 * @brief  获取性能统计信息
 * @param  stats: 性能统计结构体指针
 */
void BNO080_GetPerformanceStats(BNO080_PerformanceStats_t* stats)
{
    if (stats != NULL)
    {
        __disable_irq();
        memcpy(stats, &g_performance_stats, sizeof(BNO080_PerformanceStats_t));
        __enable_irq();
    }
}

/**
 * @brief  清除性能统计信息
 */
void BNO080_ClearPerformanceStats(void)
{
    __disable_irq();
    memset(&g_performance_stats, 0, sizeof(BNO080_PerformanceStats_t));
    __enable_irq();
    
    my_printf(&huart2, "BNO080性能统计已清除\r\n");
}

/**
 * @brief  打印诊断信息
 */
void BNO080_PrintDiagnostics(void)
{
    my_printf(&huart2, "\r\n=== BNO080诊断信息 ===\r\n");
    
    // 设备状态
    my_printf(&huart2, "设备状态: %s\r\n", g_device_online ? "在线" : "离线");
    my_printf(&huart2, "I2C地址: 0x%02X\r\n", g_bno080_i2c_address);
    my_printf(&huart2, "I2C状态: %d\r\n", g_bno080_i2c_state);
    
    // 数据状态
    my_printf(&huart2, "最后更新时间: %lu ms\r\n", g_bno080_data.last_update_time);
    my_printf(&huart2, "当前姿态: Yaw=%.2f°, Pitch=%.2f°, Roll=%.2f°\r\n", 
              g_bno080_data.yaw, g_bno080_data.pitch, g_bno080_data.roll);
    
    // 错误统计
    my_printf(&huart2, "总错误次数: %lu\r\n", g_error_stats.total_error_count);
    my_printf(&huart2, "I2C超时: %lu\r\n", g_error_stats.i2c_timeout_count);
    my_printf(&huart2, "I2C NACK: %lu\r\n", g_error_stats.i2c_nack_count);
    my_printf(&huart2, "设备离线: %lu\r\n", g_error_stats.device_offline_count);
    my_printf(&huart2, "无效数据: %lu\r\n", g_error_stats.invalid_data_count);
    my_printf(&huart2, "恢复尝试: %lu\r\n", g_error_stats.recovery_count);
    
    // 性能统计
    my_printf(&huart2, "总读取次数: %lu\r\n", g_performance_stats.total_reads);
    my_printf(&huart2, "成功读取: %lu\r\n", g_performance_stats.successful_reads);
    my_printf(&huart2, "失败读取: %lu\r\n", g_performance_stats.failed_reads);
    my_printf(&huart2, "平均读取时间: %lu μs\r\n", g_performance_stats.average_read_time_us);
    my_printf(&huart2, "数据更新率: %lu Hz\r\n", g_performance_stats.data_update_rate_hz);
    
    my_printf(&huart2, "=== 诊断信息结束 ===\r\n\r\n");
}

/**
 * @brief  测试模式
 */
void BNO080_TestMode(void)
{
    my_printf(&huart2, "\r\n=== BNO080测试模式 ===\r\n");
    
    // 测试I2C通信
    my_printf(&huart2, "测试I2C通信...\r\n");
    if (BNO080_I2C_IsReady() == HAL_OK)
    {
        my_printf(&huart2, "✓ I2C通信正常\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ I2C通信失败\r\n");
    }
    
    // 测试设备复位
    my_printf(&huart2, "测试设备复位...\r\n");
    if (BNO080_I2C_Reset() == HAL_OK)
    {
        my_printf(&huart2, "✓ 设备复位成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 设备复位失败\r\n");
    }
    
    // 等待复位完成
    HAL_Delay(200);
    
    // 测试状态检查
    my_printf(&huart2, "测试状态检查...\r\n");
    if (BNO080_I2C_CheckStatus() == HAL_OK)
    {
        my_printf(&huart2, "✓ 设备状态正常\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 设备状态异常\r\n");
    }
    
    // 测试产品ID读取
    my_printf(&huart2, "测试产品ID读取...\r\n");
    uint8_t product_id[20] = {0};
    uint16_t id_length = 0;
    if (BNO080_I2C_ReadProductID(product_id, &id_length) == HAL_OK)
    {
        my_printf(&huart2, "✓ 产品ID读取成功: ");
        for (uint16_t i = 0; i < id_length; i++)
        {
            my_printf(&huart2, "%02X ", product_id[i]);
        }
        my_printf(&huart2, "\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 产品ID读取失败\r\n");
    }
    
    // 测试传感器配置
    my_printf(&huart2, "测试传感器配置...\r\n");
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
    {
        my_printf(&huart2, "✓ 旋转向量配置成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 旋转向量配置失败\r\n");
    }
    
    // 测试数据读取
    my_printf(&huart2, "测试数据读取...\r\n");
    uint8_t test_buffer[32] = {0};
    if (BNO080_I2C_ReadSensorData(test_buffer, sizeof(test_buffer)) == HAL_OK)
    {
        my_printf(&huart2, "✓ 数据读取成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据读取失败\r\n");
    }
    
    my_printf(&huart2, "=== 测试模式结束 ===\r\n\r\n");
}